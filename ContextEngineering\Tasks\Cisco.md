
**Consulte ce fichier aussi souvent que possible lorsque tu commences une tâche, pendant la tâche et à la fin de la tâche, tu dois toujours vérifier ce fichier. Entre temps, je peux te donner des infos et des instructions supplémentaires N'écris rien dans ce fichier. Ce fichier m'appartient. C'est simplement un fichier pour dialoguer avec toi pour des tâches supplémentaires, en te décrivant les étapes avec précision.**
ContextEngineering\Tasks\Cisco.md


Avec mon approbation écrite, tu peux commencer à travailler sur la tâche.

**Alors attention, gardez bien ça en mémoire, le DOM élément très important, mettez ça en mémoire, parce que pour le soleil, tout à l'heure, on va corriger le soleil, et à mon avis, il va être au même endroit, dans Astronomical Layer. Voilà, donc pareil pour le soleil, le soleil, à mon avis, il faudra le mettre dans le même DOM élément.** 





**public\Clouds**
Voici la toute nouvelle collection de nuages que je viens d'acheter sur Evento. Donc tu peux laisser tomber les anciens nuages et tu vas plutôt piocher dans ce dossier. Il y en a, je crois, une vingtaine et tu utilises un lazy load parce qu'ils sont de très très haute qualité. Je pense qu'il y en a suffisamment et ça évitera de multiplier des nuages à l'infini. A toi de choisir s'il faut garder les anciennes animations des nuages ou s'il faut repartir de zéro avec ces nouveaux nuages. Les anciens fichiers de nuages, tu peux les supprimer ? Ou alors tu me le dis et je le fais. 

Pour les nuages, il est possible aussi d'utiliser un mode aléatoire. C'est tout à fait possible. Comme ça, dans chaque mode, on n'a jamais les mêmes configurations. C'est ça qui peut être bien. 


Faire une vérification pour les étoiles. Attention, les étoiles ne doivent apparaître que lorsqu'on clique sur Nuit Profonde. Le problème, là, si on clique sur un autre bouton, Autres, les étoiles, on les voit apparentes et après elles disparaissent. Non, il faut que les étoiles soient fonctionnelles que sur le mode Nuit Profonde. Et c'est tout.

Et je crois qu'il y en a un autre, c'est le crépuscule. Je crois que c'est les deux seuls boutons : crépuscule, on les voit légèrement, et Nuit Profonde, on voit toutes les étoiles. 


Ensuite maintenant que nous avons tout nettoyé l'ancien code il va falloir remettre en place le soleil  

Mais je suis en train de chercher sur le net s'il n'y a pas une solution avec un soleil totalement animé avec du javascript 






































































































